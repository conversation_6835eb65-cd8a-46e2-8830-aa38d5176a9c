# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
ETCD-based distributed coordinator for NIXL KV transfers.

This module provides distributed coordination capabilities for NIXL connectors
using ETCD as the metadata store and service discovery mechanism.
"""

import json
import threading
import time
import uuid
from typing import Any, Dict, List, Optional, Set

from vllm import envs
from vllm.logger import init_logger

logger = init_logger(__name__)

try:
    import pyetcd
    ETCD_AVAILABLE = True
except ImportError:
    logger.warning("pyetcd library not available. NIXL ETCD coordination disabled.")
    ETCD_AVAILABLE = False


class NixlEtcdCoordinator:
    """
    ETCD-based coordinator for NIXL distributed KV transfers.
    
    Provides service discovery, metadata exchange, and coordination
    between distributed NIXL agents.
    """
    
    def __init__(self, engine_id: str, tp_rank: int = 0, tp_size: int = 1):
        """
        Initialize ETCD coordinator.
        
        Args:
            engine_id: Unique identifier for this engine instance
            tp_rank: Tensor parallel rank
            tp_size: Tensor parallel size
        """
        if not ETCD_AVAILABLE:
            raise RuntimeError("pyetcd library is required for NIXL ETCD coordination")
        
        if not envs.VLLM_NIXL_USE_ETCD:
            raise RuntimeError("ETCD coordination is disabled. Set VLLM_NIXL_USE_ETCD=1")
        
        self.engine_id = engine_id
        self.tp_rank = tp_rank
        self.tp_size = tp_size
        self.node_id = f"{engine_id}_rank_{tp_rank}"
        
        # Parse ETCD endpoints
        endpoints_str = envs.VLLM_NIXL_ETCD_ENDPOINTS
        if not endpoints_str:
            raise ValueError("VLLM_NIXL_ETCD_ENDPOINTS must be set when using ETCD coordination")
        
        self.endpoints = [ep.strip() for ep in endpoints_str.split(",")]
        self.namespace = envs.VLLM_NIXL_ETCD_NAMESPACE
        self.timeout = envs.VLLM_NIXL_ETCD_TIMEOUT
        
        # Initialize ETCD client
        self.etcd_client: Optional[pyetcd.Etcd3Client] = None
        self._connect_etcd()
        
        # Metadata storage
        self.local_metadata: Dict[str, Any] = {}
        self.remote_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Coordination state
        self._registered = False
        self._watch_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
        
        logger.info(
            "Initialized NIXL ETCD coordinator: engine_id=%s, node_id=%s, "
            "endpoints=%s, namespace=%s", 
            self.engine_id, self.node_id, self.endpoints, self.namespace
        )
    
    def _connect_etcd(self) -> None:
        """Connect to ETCD cluster."""
        try:
            # Parse first endpoint for connection
            endpoint = self.endpoints[0]
            if endpoint.startswith("http://"):
                host_port = endpoint[7:]
            elif endpoint.startswith("https://"):
                host_port = endpoint[8:]
            else:
                host_port = endpoint

            if ":" in host_port:
                host, port = host_port.split(":", 1)
                port = int(port)
            else:
                host = host_port
                port = 2379

            self.etcd_client = pyetcd.client(
                host=host,
                port=port,
                timeout=self.timeout
            )

            # Test connection
            self.etcd_client.status()
            logger.info("Successfully connected to ETCD at %s:%d", host, port)

        except Exception as e:
            logger.error("Failed to connect to ETCD: %s", e)
            raise RuntimeError(f"ETCD connection failed: {e}") from e
    
    def register_agent(self, metadata: Dict[str, Any]) -> None:
        """
        Register this agent with ETCD.
        
        Args:
            metadata: Agent metadata to register
        """
        if not self.etcd_client:
            raise RuntimeError("ETCD client not initialized")
        
        self.local_metadata = metadata.copy()
        self.local_metadata.update({
            "engine_id": self.engine_id,
            "tp_rank": self.tp_rank,
            "tp_size": self.tp_size,
            "node_id": self.node_id,
            "timestamp": time.time(),
            "status": "active"
        })
        
        # Store metadata in ETCD
        key = f"{self.namespace}/{self.engine_id}/rank_{self.tp_rank}"
        value = json.dumps(self.local_metadata)
        
        try:
            # Use lease for automatic cleanup
            lease = self.etcd_client.lease(ttl=30)  # 30 second TTL
            self.etcd_client.put(key, value, lease=lease)

            # Keep lease alive
            self._keep_lease_alive(lease)

            self._registered = True
            logger.info("Registered agent metadata in ETCD: %s", key)

            # Start watching for other agents
            self._start_metadata_watch()

        except Exception as e:
            logger.error("Failed to register agent metadata: %s", e)
            raise RuntimeError(f"Agent registration failed: {e}") from e
    
    def _keep_lease_alive(self, lease) -> None:
        """Keep ETCD lease alive in background thread."""
        def refresh_lease():
            while not self._shutdown_event.is_set():
                try:
                    lease.refresh()
                    time.sleep(10)  # Refresh every 10 seconds
                except Exception as e:
                    logger.warning("Failed to refresh ETCD lease: %s", e)
                    break

        thread = threading.Thread(target=refresh_lease, daemon=True)
        thread.start()
    
    def _start_metadata_watch(self) -> None:
        """Start watching for metadata changes from other agents."""
        if self._watch_thread and self._watch_thread.is_alive():
            return

        def watch_metadata():
            try:
                watch_key = f"{self.namespace}/"
                events_iterator, cancel = self.etcd_client.watch_prefix(watch_key)

                for event in events_iterator:
                    if self._shutdown_event.is_set():
                        break

                    try:
                        key = event.key.decode('utf-8')
                        if event.value:
                            metadata = json.loads(event.value.decode('utf-8'))
                            engine_id = metadata.get("engine_id")

                            if engine_id and engine_id != self.engine_id:
                                self.remote_metadata[engine_id] = metadata
                                logger.debug("Updated remote metadata for engine %s", engine_id)
                        else:
                            # Key deleted
                            for engine_id in list(self.remote_metadata.keys()):
                                if key.endswith(f"/{engine_id}/rank_{self.remote_metadata[engine_id].get('tp_rank', 0)}"):
                                    del self.remote_metadata[engine_id]
                                    logger.debug("Removed metadata for engine %s", engine_id)

                    except Exception as e:
                        logger.warning("Error processing metadata watch event: %s", e)

            except Exception as e:
                logger.error("Metadata watch failed: %s", e)

        self._watch_thread = threading.Thread(target=watch_metadata, daemon=True)
        self._watch_thread.start()
        logger.info("Started metadata watch thread")
    
    def discover_agents(self, target_engine_id: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Discover available agents.
        
        Args:
            target_engine_id: Specific engine ID to discover, or None for all
            
        Returns:
            Dictionary mapping engine_id to metadata
        """
        if not self.etcd_client:
            raise RuntimeError("ETCD client not initialized")
        
        try:
            prefix = f"{self.namespace}/"
            if target_engine_id:
                prefix += f"{target_engine_id}/"

            discovered = {}
            for value, metadata in self.etcd_client.get_prefix(prefix):
                if value:
                    agent_metadata = json.loads(value.decode('utf-8'))
                    engine_id = agent_metadata.get("engine_id")
                    if engine_id and (not target_engine_id or engine_id == target_engine_id):
                        discovered[engine_id] = agent_metadata

            logger.debug("Discovered %d agents", len(discovered))
            return discovered

        except Exception as e:
            logger.error("Agent discovery failed: %s", e)
            return {}
    
    def get_agent_metadata(self, engine_id: str, tp_rank: int = 0) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a specific agent.
        
        Args:
            engine_id: Target engine ID
            tp_rank: Target tensor parallel rank
            
        Returns:
            Agent metadata or None if not found
        """
        # Check cached metadata first
        if engine_id in self.remote_metadata:
            metadata = self.remote_metadata[engine_id]
            if metadata.get("tp_rank") == tp_rank:
                return metadata
        
        # Query ETCD directly
        try:
            key = f"{self.namespace}/{engine_id}/rank_{tp_rank}"
            value, _ = self.etcd_client.get(key)
            if value:
                return json.loads(value.decode('utf-8'))
        except Exception as e:
            logger.warning("Failed to get agent metadata for %s rank %d: %s",
                          engine_id, tp_rank, e)
        
        return None
    
    def wait_for_agents(self, target_engines: List[str], timeout: float = 60.0) -> bool:
        """
        Wait for specific agents to become available.
        
        Args:
            target_engines: List of engine IDs to wait for
            timeout: Maximum time to wait in seconds
            
        Returns:
            True if all agents are available, False on timeout
        """
        start_time = time.time()
        missing_engines = set(target_engines)
        
        while missing_engines and (time.time() - start_time) < timeout:
            discovered = self.discover_agents()
            missing_engines = missing_engines - set(discovered.keys())
            
            if missing_engines:
                logger.debug("Still waiting for engines: %s", list(missing_engines))
                time.sleep(1.0)
        
        if missing_engines:
            logger.warning("Timeout waiting for engines: %s", list(missing_engines))
            return False
        
        logger.info("All target engines are available: %s", target_engines)
        return True
    
    def shutdown(self) -> None:
        """Shutdown the coordinator and cleanup resources."""
        logger.info("Shutting down NIXL ETCD coordinator")
        
        self._shutdown_event.set()
        
        # Remove our metadata from ETCD
        if self.etcd_client and self._registered:
            try:
                key = f"{self.namespace}/{self.engine_id}/rank_{self.tp_rank}"
                self.etcd_client.delete(key)
                logger.info("Removed agent metadata from ETCD")
            except Exception as e:
                logger.warning("Failed to remove metadata from ETCD: %s", e)
        
        # Wait for watch thread to finish
        if self._watch_thread and self._watch_thread.is_alive():
            self._watch_thread.join(timeout=5.0)
        
        # Close ETCD client
        if self.etcd_client:
            try:
                self.etcd_client.close()
            except Exception as e:
                logger.warning("Error closing ETCD client: %s", e)
        
        logger.info("NIXL ETCD coordinator shutdown complete")
