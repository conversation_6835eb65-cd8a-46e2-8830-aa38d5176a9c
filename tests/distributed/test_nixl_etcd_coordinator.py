"""
Tests for NIXL ETCD coordinator functionality.

These tests verify the ETCD-based distributed coordination for NIXL connectors.
"""

import json
import os
import pytest
import threading
import time
from unittest.mock import MagicMock, patch

from vllm.distributed.kv_transfer.kv_connector.v1.nixl_etcd_coordinator import (
    NixlEtcdCoordinator, ETCD_AVAILABLE)


@pytest.fixture
def mock_etcd_client():
    """Mock ETCD client for testing."""
    with patch('vllm.distributed.kv_transfer.kv_connector.v1.nixl_etcd_coordinator.pyetcd') as mock_etcd3:
        mock_client = MagicMock()
        mock_etcd3.client.return_value = mock_client

        # Mock basic operations
        mock_client.status.return_value = {"version": "3.5.1"}
        mock_client.put.return_value = True
        mock_client.get.return_value = (None, None)
        mock_client.get_prefix.return_value = []
        mock_client.delete.return_value = True
        mock_client.lease.return_value = MagicMock()
        mock_client.watch_prefix.return_value = (iter([]), lambda: None)

        yield mock_client


@pytest.fixture
def etcd_env():
    """Set up ETCD environment variables for testing."""
    original_env = {}
    test_env = {
        "VLLM_NIXL_USE_ETCD": "1",
        "VLLM_NIXL_ETCD_ENDPOINTS": "http://localhost:2379",
        "VLLM_NIXL_ETCD_NAMESPACE": "/test/nixl/agents",
        "VLLM_NIXL_ETCD_TIMEOUT": "5"
    }
    
    # Save original values
    for key in test_env:
        original_env[key] = os.environ.get(key)
        os.environ[key] = test_env[key]
    
    yield test_env
    
    # Restore original values
    for key, value in original_env.items():
        if value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = value


@pytest.mark.skipif(not ETCD_AVAILABLE, reason="etcd3 library not available")
class TestNixlEtcdCoordinator:
    """Test cases for NixlEtcdCoordinator."""
    
    def test_initialization(self, mock_etcd_client, etcd_env):
        """Test coordinator initialization."""
        coordinator = NixlEtcdCoordinator(
            engine_id="test_engine",
            tp_rank=0,
            tp_size=1
        )
        
        assert coordinator.engine_id == "test_engine"
        assert coordinator.tp_rank == 0
        assert coordinator.tp_size == 1
        assert coordinator.node_id == "test_engine_rank_0"
        assert coordinator.namespace == "/test/nixl/agents"
        assert coordinator.timeout == 5
        
        # Verify ETCD client was created
        mock_etcd_client.status.assert_called_once()
    
    def test_initialization_without_etcd_env(self, mock_etcd_client):
        """Test initialization fails without ETCD environment."""
        with pytest.raises(RuntimeError, match="ETCD coordination is disabled"):
            NixlEtcdCoordinator(
                engine_id="test_engine",
                tp_rank=0,
                tp_size=1
            )
    
    def test_initialization_without_endpoints(self, mock_etcd_client):
        """Test initialization fails without ETCD endpoints."""
        os.environ["VLLM_NIXL_USE_ETCD"] = "1"
        os.environ.pop("VLLM_NIXL_ETCD_ENDPOINTS", None)
        
        with pytest.raises(ValueError, match="VLLM_NIXL_ETCD_ENDPOINTS must be set"):
            NixlEtcdCoordinator(
                engine_id="test_engine",
                tp_rank=0,
                tp_size=1
            )
    
    def test_register_agent(self, mock_etcd_client, etcd_env):
        """Test agent registration."""
        coordinator = NixlEtcdCoordinator(
            engine_id="test_engine",
            tp_rank=0,
            tp_size=1
        )
        
        metadata = {
            "agent_metadata": "test_metadata",
            "kv_caches_base_addr": [12345],
            "num_blocks": 100,
            "block_len": 1024,
            "attn_backend_name": "FLASHINFER_VLLM_V1"
        }
        
        coordinator.register_agent(metadata)
        
        # Verify put was called with correct key and value
        mock_etcd_client.put.assert_called()
        call_args = mock_etcd_client.put.call_args
        key = call_args[0][0]
        value = call_args[0][1]
        
        assert key == "/test/nixl/agents/test_engine/rank_0"
        
        # Parse and verify the stored metadata
        stored_metadata = json.loads(value)
        assert stored_metadata["engine_id"] == "test_engine"
        assert stored_metadata["tp_rank"] == 0
        assert stored_metadata["tp_size"] == 1
        assert stored_metadata["status"] == "active"
        assert "timestamp" in stored_metadata
    
    def test_discover_agents(self, mock_etcd_client, etcd_env):
        """Test agent discovery."""
        coordinator = NixlEtcdCoordinator(
            engine_id="test_engine",
            tp_rank=0,
            tp_size=1
        )
        
        # Mock ETCD response
        mock_metadata = {
            "engine_id": "remote_engine",
            "tp_rank": 0,
            "tp_size": 1,
            "status": "active"
        }
        mock_etcd_client.get_prefix.return_value = [
            (json.dumps(mock_metadata).encode(), None)
        ]
        
        discovered = coordinator.discover_agents()
        
        assert "remote_engine" in discovered
        assert discovered["remote_engine"]["engine_id"] == "remote_engine"
        
        # Verify get_prefix was called with correct prefix
        mock_etcd_client.get_prefix.assert_called_with("/test/nixl/agents/")
    
    def test_discover_specific_agent(self, mock_etcd_client, etcd_env):
        """Test discovery of specific agent."""
        coordinator = NixlEtcdCoordinator(
            engine_id="test_engine",
            tp_rank=0,
            tp_size=1
        )
        
        # Mock ETCD response for specific engine
        mock_metadata = {
            "engine_id": "target_engine",
            "tp_rank": 0,
            "tp_size": 1,
            "status": "active"
        }
        mock_etcd_client.get_prefix.return_value = [
            (json.dumps(mock_metadata).encode(), None)
        ]
        
        discovered = coordinator.discover_agents("target_engine")
        
        assert "target_engine" in discovered
        
        # Verify get_prefix was called with specific engine prefix
        mock_etcd_client.get_prefix.assert_called_with("/test/nixl/agents/target_engine/")
    
    def test_get_agent_metadata(self, mock_etcd_client, etcd_env):
        """Test getting specific agent metadata."""
        coordinator = NixlEtcdCoordinator(
            engine_id="test_engine",
            tp_rank=0,
            tp_size=1
        )
        
        # Mock ETCD response
        mock_metadata = {
            "engine_id": "remote_engine",
            "tp_rank": 1,
            "tp_size": 2,
            "status": "active"
        }
        mock_etcd_client.get.return_value = (
            json.dumps(mock_metadata).encode(),
            None
        )
        
        metadata = coordinator.get_agent_metadata("remote_engine", 1)
        
        assert metadata is not None
        assert metadata["engine_id"] == "remote_engine"
        assert metadata["tp_rank"] == 1
        
        # Verify get was called with correct key
        mock_etcd_client.get.assert_called_with("/test/nixl/agents/remote_engine/rank_1")
    
    def test_get_agent_metadata_not_found(self, mock_etcd_client, etcd_env):
        """Test getting metadata for non-existent agent."""
        coordinator = NixlEtcdCoordinator(
            engine_id="test_engine",
            tp_rank=0,
            tp_size=1
        )
        
        # Mock ETCD response - no data
        mock_etcd_client.get.return_value = (None, None)
        
        metadata = coordinator.get_agent_metadata("nonexistent_engine", 0)
        
        assert metadata is None
    
    def test_wait_for_agents_success(self, mock_etcd_client, etcd_env):
        """Test waiting for agents successfully."""
        coordinator = NixlEtcdCoordinator(
            engine_id="test_engine",
            tp_rank=0,
            tp_size=1
        )
        
        # Mock discovery to return target engines after some delay
        def mock_discover(target_engine_id=None):
            if hasattr(mock_discover, 'call_count'):
                mock_discover.call_count += 1
            else:
                mock_discover.call_count = 1
            
            if mock_discover.call_count >= 2:
                return {"target_engine": {"engine_id": "target_engine"}}
            return {}
        
        coordinator.discover_agents = mock_discover
        
        result = coordinator.wait_for_agents(["target_engine"], timeout=5.0)
        
        assert result is True
    
    def test_wait_for_agents_timeout(self, mock_etcd_client, etcd_env):
        """Test waiting for agents with timeout."""
        coordinator = NixlEtcdCoordinator(
            engine_id="test_engine",
            tp_rank=0,
            tp_size=1
        )
        
        # Mock discovery to never return target engines
        coordinator.discover_agents = lambda target_engine_id=None: {}
        
        result = coordinator.wait_for_agents(["nonexistent_engine"], timeout=1.0)
        
        assert result is False
    
    def test_shutdown(self, mock_etcd_client, etcd_env):
        """Test coordinator shutdown."""
        coordinator = NixlEtcdCoordinator(
            engine_id="test_engine",
            tp_rank=0,
            tp_size=1
        )
        
        # Register agent first
        coordinator.register_agent({"test": "metadata"})
        coordinator._registered = True
        
        coordinator.shutdown()
        
        # Verify delete was called to remove metadata
        mock_etcd_client.delete.assert_called_with("/test/nixl/agents/test_engine/rank_0")

        # Verify client was closed
        mock_etcd_client.close.assert_called_once()


@pytest.mark.skipif(ETCD_AVAILABLE, reason="Testing behavior when etcd3 is not available")
class TestNixlEtcdCoordinatorWithoutEtcd:
    """Test behavior when etcd3 library is not available."""
    
    def test_initialization_without_etcd_library(self):
        """Test initialization fails gracefully without etcd3 library."""
        os.environ["VLLM_NIXL_USE_ETCD"] = "1"
        os.environ["VLLM_NIXL_ETCD_ENDPOINTS"] = "http://localhost:2379"
        
        with pytest.raises(RuntimeError, match="etcd3 library is required"):
            NixlEtcdCoordinator(
                engine_id="test_engine",
                tp_rank=0,
                tp_size=1
            )
