#!/usr/bin/env python3
"""
Test script to verify pyetcd integration with vLLM NIXL coordinator.

This script tests the basic functionality of the NIXL ETCD coordinator
using the pyetcd library (etcd-sdk-python).
"""

import json
import os
import sys
import time
from typing import Dict, Any

# Add the vllm directory to the path
sys.path.insert(0, '/Users/<USER>/py-workspace/github.com/lengrongfu/vllm')

def test_pyetcd_import():
    """Test that pyetcd can be imported and basic client creation works."""
    try:
        import pyetcd
        print(f"✓ pyetcd imported successfully")
        
        # Test client creation (without connecting to actual etcd)
        client = pyetcd.client(host='localhost', port=2379)
        print(f"✓ pyetcd client created: {type(client)}")
        
        # Check available methods
        methods = [method for method in dir(client) if not method.startswith('_')]
        print(f"✓ Available client methods: {methods[:10]}...")  # Show first 10 methods
        
        return True
    except ImportError as e:
        print(f"✗ Failed to import pyetcd: {e}")
        return False
    except Exception as e:
        print(f"✗ Error testing pyetcd: {e}")
        return False

def test_nixl_coordinator_import():
    """Test that the NIXL coordinator can be imported."""
    try:
        from vllm.distributed.kv_transfer.kv_connector.v1.nixl_etcd_coordinator import (
            NixlEtcdCoordinator, ETCD_AVAILABLE
        )
        print(f"✓ NixlEtcdCoordinator imported successfully")
        print(f"✓ ETCD_AVAILABLE: {ETCD_AVAILABLE}")
        return True
    except ImportError as e:
        print(f"✗ Failed to import NixlEtcdCoordinator: {e}")
        return False
    except Exception as e:
        print(f"✗ Error importing NixlEtcdCoordinator: {e}")
        return False

def test_coordinator_without_etcd_server():
    """Test coordinator initialization without actual ETCD server."""
    try:
        # Set up environment variables
        os.environ["VLLM_NIXL_USE_ETCD"] = "1"
        os.environ["VLLM_NIXL_ETCD_ENDPOINTS"] = "http://localhost:2379"
        os.environ["VLLM_NIXL_ETCD_NAMESPACE"] = "/test/nixl/agents"
        os.environ["VLLM_NIXL_ETCD_TIMEOUT"] = "5"
        
        from vllm.distributed.kv_transfer.kv_connector.v1.nixl_etcd_coordinator import (
            NixlEtcdCoordinator
        )
        
        # This should fail because no ETCD server is running, but it should fail gracefully
        try:
            coordinator = NixlEtcdCoordinator(
                engine_id="test_engine",
                tp_rank=0,
                tp_size=1
            )
            print("✗ Coordinator creation should have failed without ETCD server")
            return False
        except RuntimeError as e:
            if "ETCD connection failed" in str(e):
                print(f"✓ Coordinator correctly failed without ETCD server: {e}")
                return True
            else:
                print(f"✗ Unexpected error: {e}")
                return False
                
    except Exception as e:
        print(f"✗ Error testing coordinator: {e}")
        return False

def test_api_compatibility():
    """Test that the pyetcd API matches our expectations."""
    try:
        import pyetcd
        
        # Create a client (won't connect without server)
        client = pyetcd.client()
        
        # Check that required methods exist
        required_methods = ['get', 'put', 'delete', 'get_prefix', 'delete_prefix', 
                          'status', 'lease', 'watch_prefix', 'close']
        missing_methods = []
        
        for method in required_methods:
            if not hasattr(client, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"✗ Missing required methods: {missing_methods}")
            return False
        
        print(f"✓ All required methods are available: {required_methods}")
        
        # Check that we have the members property for connection testing
        if hasattr(client, 'members'):
            print("✓ 'members' property available")
        else:
            print("! 'members' property not available (not critical)")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing API compatibility: {e}")
        return False

def test_pyetcd_features():
    """Test specific pyetcd features that we use."""
    try:
        import pyetcd
        
        # Test client creation with various parameters
        client = pyetcd.client(host='localhost', port=2379, timeout=5)
        print("✓ Client creation with parameters works")
        
        # Test that lease creation method exists and returns expected type
        try:
            # This will fail without server, but we can check the method exists
            lease = client.lease(ttl=30)
            print("✗ Lease creation should have failed without server")
            return False
        except Exception:
            print("✓ Lease method exists and fails appropriately without server")
        
        # Test that watch_prefix method exists
        try:
            events_iterator, cancel = client.watch_prefix("/test/")
            print("✗ Watch should have failed without server")
            return False
        except Exception:
            print("✓ Watch_prefix method exists and fails appropriately without server")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing pyetcd features: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing pyetcd integration with vLLM NIXL coordinator")
    print("=" * 60)
    
    tests = [
        ("pyetcd Import", test_pyetcd_import),
        ("NIXL Coordinator Import", test_nixl_coordinator_import),
        ("API Compatibility", test_api_compatibility),
        ("pyetcd Features", test_pyetcd_features),
        ("Coordinator without ETCD Server", test_coordinator_without_etcd_server),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        if test_func():
            passed += 1
        else:
            print(f"Test '{test_name}' failed!")
    
    print("\n" + "=" * 60)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! pyetcd integration is working correctly.")
        return 0
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
